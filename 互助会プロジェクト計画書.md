---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---
# 互助会システム プロジェクト計画書（草案）

## 目次
1. [プロジェクト概要](#1-プロジェクト概要)
   - 1.1 [背景と目的](#11-背景と目的)
   - 1.2 [プロジェクト範囲](#12-プロジェクト範囲)
   - 1.3 [主要納品物](#13-主要納品物)
2. [プロジェクト体制](#2-プロジェクト体制)
   - 2.1 [組織構成](#21-組織構成)
   - 2.2 [役割と責任](#22-役割と責任)
3. [プロジェクト計画](#3-プロジェクト計画)
   - 3.1 [全体スケジュール](#31-全体スケジュール)
   - 3.2 [主要マイルストーン](#32-主要マイルストーン)
4. [リソース計画](#4-リソース計画)
   - 4.1 [人員計画](#41-人員計画)
   - 4.2.1 [イニシャル予算（初期開発費用）](#421-イニシャル予算初期開発費用)
   - 4.2.2 [ランニング予算（運用保守費用）](#422-ランニング予算運用保守費用)
   - 4.2.3 [総予算（イニシャル + ランニング5年分）](#423-総予算イニシャル--ランニング5年分)
5. [フェーズ別リソース計画](#5-フェーズ別リソース計画)
6. [品質管理計画](#6-品質管理計画)
   - 6.1 [IDCFクラウド品質基準](#61-idcfクラウド品質基準)
   - 6.2 [品質ゲート](#62-品質ゲート)
7. [ステークホルダー管理](#7-ステークホルダー管理)
   - 7.1 [顧客側ステークホルダー](#71-顧客側ステークホルダー)
   - 7.2 [ベンダー側ステークホルダー](#72-ベンダー側ステークホルダー)
8. [リスク管理](#8-リスク管理)
   - 8.1 [技術的リスク](#81-技術的リスク)
   - 8.2 [プロジェクト管理リスク](#82-プロジェクト管理リスク)
   - 8.3 [移行リスク](#83-移行リスク)
9. [コミュニケーション計画](#9-コミュニケーション計画)
   - 9.1 [会議体制](#91-会議体制)
10. [変更管理](#10-変更管理)
    - 10.1 [変更管理プロセス](#101-変更管理プロセス)
11. [プロジェクト管理ツール](#11-プロジェクト管理ツール)


## 1. プロジェクト概要

**注意事項**: 本計画書に記載の予算は、顧客規模（30万会員）および当社のシステム導入実績に基づいた概算です。この概算予算をもとに、スケジュールと人員計画を立案しております。要件定義後に工数が大きく変動する可能性があるため、正式な予算とスケジュールは要件定義完了後に改めて提出いたします。

### 1.1 背景と目的
- **背景**: 30万会員を抱える現行システムの老朽化と機能拡張の必要性
- **目的**:
  - 新規パッケージシステムの開発・販売
  - 業務効率化と顧客体験向上
  - セキュリティ強化と拡張可能なアーキテクチャ構築

### 1.2 プロジェクト範囲
- **対象システム**: 互助会会員管理システムのリプレース

### 1.3 主要納品物
- 現行調査報告書
- 提案書
- 新規パッケージシステム
- 要件定義書一式
- 基本設計書一式
- 研修資料一式

## 2. プロジェクト体制

### 2.1 組織構成
- **顧客側**:
  - プロジェクトスポンサー
  - プロジェクト責任者
  - 業務責任者
  - 業務担当者
  - IT責任者
- **ベンダー側**:
  - プロジェクトマネージャー(PM)
  - プロジェクトリーダー(PL)
  - システムエンジニア(SE)
  - プログラマー（PG）
  - 営業担当者

### 2.2 役割と責任
| 役割 | 顧客側<br>ベンダー側 | 責任範囲 |
|------|------------------|----------|
| プロジェクトスポンサー | 顧客側 | 予算承認・重要意思決定 |
| プロジェクト責任者 | 顧客側 | プロジェクト統括 |
| 業務責任者 | 顧客側 | 業務要件の決定 |
| 業務担当者 | 顧客側 | 業務要件の提供 |
| IT責任者 | 顧客側 | 導入全般の支援 |
| プロジェクトマネージャー | ベンダー側 | プロジェクト統括・予算・進捗管理 |
| プロジェクトリーダー | ベンダー側 | スコープ管理・品質管理 |
| システムエンジニア | ベンダー側 | ヒアリング・提案・設計・テスト |
| プログラマー | ベンダー側 | 開発・テスト |
| 営業担当者 | ベンダー側 | 営業・折衝管理 |

## 3. プロジェクト計画

### 3.1 全体スケジュール
```mermaid
gantt
    title 互助会会員システムリプレース 全体スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section 1: 現行調査
    現行調査 :survey, 2026-02-01, 2026-04-30

    section 2: 提案
    提案 : prop, 2026-05-01, 2026-05-31

    section 3: 要件定義
    要件定義 : req, 2026-06-01, 2026-12-31

    section 4: 見積作成・顧客検討
    見積作成 : estimate, 2026-12-01, 2026-12-14
    顧客検討 : review, 2026-12-15, 2026-12-31

    section 5: 設計・開発・テスト
    設計・開発・テスト : design, 2027-01-01, 2028-03-31

    section 6: データ移行
    データ移行 : migrate, 2027-01-01, 2028-08-31

    section 7: 受入テスト
    研修・受入・並行稼働 : test, 2028-04-01, 2028-08-31

    section 8: 稼働
    稼働 : prod, 2028-09-01, 2028-12-31
```
### 3.2 主要マイルストーン
| マイルストーン | 開始予定日 | 終了予定日 | 期間 | 契約形態 | 成果物 |
|---------------|------------|------------|------|----------|--------|
| 現行調査 | 2026-02-01 | 2026-04-30 | 3ヶ月 | 準委任 | 現行調査報告書 |
| 提案 | 2026-05-01 | 2026-05-31 | 1ヶ月 | 準委任 | 提案書 |
| 要件定義 | 2026-06-01 | 2026-11-30 | 6ヶ月 | 準委任 | 要件定義書一式 |
| 見積作成 | 2026-12-01 | 2026-12-14 | 2週間 | - | 工程表<br>見積書一式 |
| 顧客検討 | 2026-12-15 | 2026-12-31 | 2週間 | - | - |
| 基本設計 | 2027-01-01 | 2027-04-30 | 4ヶ月 | 請負 | 基本設計書一式 |
| 詳細・開発・単体 | 2027-05-01 | 2027-12-31 | 8ヶ月 | 請負 | プログラム一式 |
| システムテスト | 2028-01-01 | 2028-03-31 | 3ヶ月 | 請負 | テスト結果一式|
| 研修・受入・並行 | 2028-04-01 | 2028-08-31 | 5ヶ月 | 請負 | 研修資料一式 |
| データ移行 | 2027-01-01 | 2028-08-31 | 20ヶ月 | 請負 | コンバートデータ |
| 本番稼働開始 | 2028-09-01 | - | - | - | - |

## 4. リソース計画

### 4.1 人員計画
- **総工数**: 143人月
- **ピーク時体制**:  SE3名 + PG5名 

#### 4.2.1 イニシャル予算（初期開発費用）
- **イニシャル総予算**: 127,800,000円（人月単価900,000円で計算）
- **内訳**:
  - 現行調査: 5,400,000円　(6人月)
  - 提案: 900,000円　(1人月)
  - 要件定義: 9,000,000円　(10人月)
  - 基本設計: 10,800,000円　(12人月)
  - 詳細・開発・単体: 57,600,000円　(64人月)
  - システムテスト: 17,100,000円　(19人月)
  - 研修・受入対応: 9,000,000円　(10人月)
  - 移行: 7,200,000円　(8人月)
  - 管理: 11,700,000円　(13人月、全体工数の10%程度)

#### 4.2.2 ランニング予算（運用保守費用）
- **年間ランニング費用**: 13,140,000円
- **5年間総額**: 65,700,000円

**ランニング費用内訳（年額）**:
- システム保守費用: 11,340,000円
  - 基準額: 75,600,000円（基本設計+詳細・開発・単体+移行の合計：84人月）
  - 保守率: 15%（75,600,000円 × 15% = 11,340,000円）
- サーバ使用料: 1,800,000円（月額150,000円 × 12ヶ月）

**5年間ランニング予算**:
| 年度 | システム保守費用 | サーバ使用料 | 年間合計 |
|------|------------------|--------------|----------|
| 1年目 | 11,340,000円 | 1,800,000円 | 13,140,000円 |
| 2年目 | 11,340,000円 | 1,800,000円 | 13,140,000円 |
| 3年目 | 11,340,000円 | 1,800,000円 | 13,140,000円 |
| 4年目 | 11,340,000円 | 1,800,000円 | 13,140,000円 |
| 5年目 | 11,340,000円 | 1,800,000円 | 13,140,000円 |
| **合計** | **56,700,000円** | **9,000,000円** | **65,700,000円** |

#### 4.2.3 総予算（イニシャル + ランニング5年分）
- **総予算**: 194,400,000円
  - イニシャル予算: 128,700,000円
  - ランニング予算（5年分）: 65,700,000円

## 5. フェーズ別リソース計画

| フェーズ | 期間 | 担当者数 | 総人月 |
|---------|------|----------|--------|
| 1. 現行調査・分析 | 3ヶ月 | SE2名 | 6人月 |
| 2. 提案書作成 | 2ヶ月 | SE2名 | 1人月 |
| 3. 要件定義 | 5ヶ月 | SE2名 | 10人月 |
| 4. 基本設計 | 4ヶ月 | SE3名 | 12人月 |
| 5. 詳細設計・開発・単体テスト | 8ヶ月 | SE3名+PG5名 | 64人月 |
| 6. システムテスト | 4ヶ月 | SE2名+PG4名 | 19人月 |
| 7. 研修・受入・並行稼働対応 | 5ヶ月 | SE2名+PG4名 | 10人月 |
| 8. データ移行 | 20ヶ月 | SE1名+PG1名 | 8人月 |
| 9. プロジェクト管理 | 全期間 | PM1名+PL1名 | 14人月 |
| **合計** | 約31ヶ月 | - | **143人月** |

## 6. 品質管理計画

### 6.1 IDCFクラウド品質基準

#### 6.1.1 可用性基準
- **SLA**: 99.9%（月間ダウンタイム43分以内）

#### 6.1.2 性能基準
- **レスポンスタイム**:
  - 通常処理: 3秒以内（90%ile）
  - 検索処理: 3秒以内（90%ile）
  - 帳票出力: 10秒以内（90%ile）

#### 6.1.3 セキュリティ基準
- **ネットワークセキュリティ**:
  - IDCFクラウドのファイアウォール機能
  - WAF（Web Application Firewall）検討
  - VPN接続による専用線アクセス検討
  - クライアント認証機能
  - IP制限機能
- **データセキュリティ**:
  - SSL/TLS暗号化通信（TLS1.2以上）
  - パスワード暗号化
- **アクセス制御**:
  - ロールベースアクセス制御（RBAC）
  - アクセスログの記録・監査

#### 6.1.4 運用基準
- **監視体制**: 24時間365日監視
- **バックアップ**:
  - 日次フルバックアップ
  - 遠隔地への複製保存
- **災害対策**:
  - RTO（目標復旧時間）: 2時間以内
  - RPO（目標復旧ポイント）: 24時間以内

### 6.2 品質ゲート
| ゲート | 時期 | チェック項目 |
|--------|------|--------------|
| 要件定義完了 | 2026-11 | 業務システム要件の合意 |
| 基本設計完了 | 2027-03 | 基本設計書の承認 |
| 開発完了 | 2027-11 | テスト結果確認 |
| システムテスト完了 | 2028-03 | システムテスト結果確認 |

## 7. ステークホルダー管理

### 7.1 顧客側ステークホルダー
| 役割 | 責任範囲 | 主要タスク |
|------|----------|------------|
| 経営責任者 | 予算承認・重要意思決定 | 重要意思決定 |
| プロジェクト責任者 | プロジェクト統括 | プロジェクト管理 |
| 業務責任者 | 業務要件の決定 | 要件定義・受入テスト |
| 業務担当者 | 業務要件の提供 | 要件定義・動作確認・操作性評価 |
| IT責任者 | 導入全般の支援 | 導入全般の支援・運用マニュアル作成 |

### 7.2 ベンダー側ステークホルダー
| 役割 | 責任範囲 | 主要タスク |
|------|----------|------------|
| プロジェクトマネージャー | 開発統括 | 進捗管理・リスク管理 |
| 開発リーダー | 開発工程管理 | 開発チーム統括 |
|システムエンジニア | ヒアリング・設計 | 設計・開発・テスト |
| 営業担当 | 顧客折衝 | 契約・調整業務 |

## 8. リスク管理

### 8.1 技術的リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| 現行システム情報不足 | 中 | 高 | 早期の現行システム会社との調整 |

### 8.2 プロジェクト管理リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| スケジュール遅延 | 中 | 高 | クリティカルパスのバッファ確保 |
| 品質問題 | 中 | 中 | 段階的な品質ゲートの設置 |
| 要件変更 | 高 | 中 | 変更管理プロセスの厳格化 |

### 8.3 移行リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| データ移行不備 | 中 | 高 | 十分な検証期間の確保 |
| 業務継続性問題 | 低 | 高 | 並行稼働期間の設定 |
| ユーザー受入れ問題 | 中 | 中 | 十分な教育・研修期間の確保 |

## 9. コミュニケーション計画

### 9.1 会議体制
- **現行調査**: 週2回の現地調査
- **要件定義**: 顧客と週1回の会議
- **基本設計**: 顧客と隔週の会議
- **プロジェクト進捗会議**: 週次（実務者向け）
- **技術レビュー**: 随時（設計・開発フェーズ）



## 10. 変更管理

### 10.1 変更管理プロセス
要件定義終了後の工程において変更が発生した場合は、以下の方針で対応する：

- **微調整レベルの変更**: 現在のフェーズ内で対応し、スケジュールや予算への影響を最小限に抑える
- **大きな変更**: 別フェーズとして検討し、追加の工数・期間・予算を算定した上で顧客と協議する

変更管理の手順：
1. 変更要求の提出
2. 変更規模の判定（微調整 or 大きな変更）
3. 影響度分析（工数・期間・予算への影響）
4. 承認プロセス
5. 実装と検証

## 11. プロジェクト管理ツール

1. **プロジェクト管理**: Google Spreadsheets
   - 進捗管理
   - リソース計画・予算管理
   - 課題管理

2. **開発管理**: Redmine
   - 詳細タスク管理
   - バグ・課題管理

3. **構成管理**: Subversion
   - 要件定義書・設計書管理
   - 議事録管理
   - プログラムコード管理

4. **IDCFクラウド管理**: IDCFクラウド管理コンソール
   - インフラ監視
   - リソース管理
   - セキュリティ管理



<!-- **承認欄**
| 役職                 | 氏名 | 署名 | 承認日 |
|----------------------|------|------|--------|
| プロジェクトスポンサー |      |      |        |
| プロジェクト責任者     |      |      |        |
| 業務責任者             |      |      |        |
| IT責任者               |      |      |        |
| プロジェクトマネージャー |      |      |        |
| プロジェクトリーダー   |      |      |        | -->
