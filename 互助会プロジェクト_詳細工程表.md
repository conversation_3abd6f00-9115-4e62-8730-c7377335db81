---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "0.5cm"
    bottom: "1cm"
    left: "0.8cm"
  format: "A4"
  printBackground: true
---
# 互助会システムプロジェクト 詳細工程表

## 目次
1. [プロジェクト概要](#1-プロジェクト概要)
2. [主要マイルストーン](#2-主要マイルストーン)
3. [ガントチャート](#3-ガントチャート)
4. [工程1: 現行調査・分析（準委任）](#4-工程1-現行調査分析準委任)
5. [工程2: 提案書作成・提案（準委任）](#5-工程2-提案書作成提案準委任)
6. [工程3: 要件定義（準委任）](#6-工程3-要件定義準委任)
7. [工程4: 見積作成・顧客検討](#7-工程4-見積作成顧客検討)
8. [工程5: 基本設計（請負）](#8-工程5-基本設計請負)
9. [工程6: 詳細設計・開発・単体テスト（請負）](#9-工程6-詳細設計開発単体テスト請負)
10. [工程7: システムテスト（請負）](#10-工程7-システムテスト請負)
11. [工程8: 受入対応（請負）](#11-工程8-受入対応請負)
12. [工程9: 移行（請負）](#12-工程9-移行請負)
13. [工程10: 本番稼働](#13-工程10-本番稼働)
14. [プロジェクト管理（全期間）](#14-プロジェクト管理全期間)
15. [年度別作業概要](#15-年度別作業概要)
16. [重要な判定ポイント（ゲート）](#16-重要な判定ポイントゲート)

---

## 1. プロジェクト概要

**注意事項**: 本計画書に記載の予算は、顧客規模（30万会員）および当社のシステム導入実績に基づいた概算です。この概算予算をもとに、スケジュールと人員計画を立案しております。要件定義後に工数が大きく変動する可能性があるため、正式な予算とスケジュールは要件定義完了後に改めて提出いたします。

- **対象システム**: 互助会会員システム
- **会員規模**: 30万会員
- **契約形態**: 現行調査・要件定義（準委任）、基本設計以降（請負）
- **稼働予定**: 2028年9月
- **移行方式**: 並行稼働後、一括データ移行
- **インフラ**: IDCFクラウド
- **総工数**: 144人月
- **総予算**: 129,600,000円（人月単価900,000円）
---

## 2. 主要マイルストーン

| マイルストーン | 開始予定日 | 完了予定日 | 期間 | 主要成果物 | 工数 |
|---------------|------------|------------|------|------------|------|
| プロジェクト開始 | 2026年2月1日 | 2026年2月1日 | - | プロジェクト計画書 | - |
| 現行調査 | 2026年2月1日 | 2026年4月30日 | 3ヶ月 | 調査報告書 | 6人月 |
| 提案書 | 2026年5月1日 | 2026年5月31日 | 1ヶ月 | 提案書 | 1人月 |
| 要件定義 | 2026年6月1日 | 2026年11月30日 | 6ヶ月 | 要件定義書一式 | 10人月 |
| 見積作成 | 2026年12月1日 | 2026年12月14日 | 2週間 | 見積書 | 0人月 |
| 顧客検討 | 2026年12月15日 | 2026年12月31日 | 2週間 | - | 0人月 |
| 基本設計 | 2027年1月1日 | 2027年4月30日 | 4ヶ月 | 基本設計書一式 | 12人月 |
| 詳細設計・開発 | 2027年5月1日 | 2027年12月31日 | 8ヶ月 | プログラム一式 | 64人月 |
| システムテスト | 2028年1月1日 | 2028年3月31日 | 3ヶ月 | テスト結果書一式 | 20人月 |
| 研修・受入 | 2028年4月1日 | 2028年8月31日 | 5ヶ月 | 研修資料一式 | 10人月 |
| データ移行 | 2027年1月1日 | 2028年8月31日 | 20ヶ月 | コンバートデータ | 8人月 |
| 本番稼働開始 | 2028年9月1日 | - | - | - | - |
| プロジェクト管理 | 2026年2月1日 | 2028年8月31日 | 全期間 | 各種管理資料 | 13人月 |

**合計期間**: 約31ヶ月（2年7ヶ月）
**総工数**: 144人月
**総金額**: 129,600,000円

---

## 3. ガントチャート

```mermaid
gantt
    title 互助会会員システム スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section 工程1: 現行調査
    プロジェクト立ち上げ           :milestone, m1, 2026-02-01, 0d
    現行システム調査              :survey1, 2026-02-15, 2026-03-31
    業務調査・ヒアリング          :survey2, 2026-04-01, 2026-04-15
    現行調査報告書作成            :survey3, 2026-04-16, 2026-04-30
    現行調査完了                  :milestone, m2, 2026-04-30, 0d

    section 工程2: 提案
    システム提案書作成            :prop1, 2026-05-01, 2026-05-31
    提案書完了                    :milestone, m3, 2026-05-31, 0d

    section 工程3: 要件定義
    要件定義計画・体制構築        :def1, 2026-06-01, 2026-06-30
    業務要件定義                  :def2, 2026-07-01, 2026-09-15
    システム要件定義              :def3, 2026-09-16, 2026-10-31
    移行要件定義                  :def4, 2026-11-01, 2026-11-30
    要件定義完了                  :milestone, m4, 2026-11-30, 0d

    section 工程4: 見積・検討
    見積作成                      :estimate1, 2026-12-01, 2026-12-14
    顧客検討                      :review1, 2026-12-15, 2026-12-31
    見積・検討完了                :milestone, m5, 2026-12-31, 0d

    section 工程5: 基本設計
    システム構成設計              :design1, 2027-01-01, 2027-02-28
    機能設計・画面設計            :design2, 2027-03-01, 2027-04-30
    基本設計完了                  :milestone, m6, 2027-04-30, 0d

    section 工程6: 詳細設計・開発
    詳細設計                      :dev1, 2027-05-01, 2027-06-30
    プログラム開発                :dev2, 2027-07-01, 2027-11-30
    単体テスト                    :dev3, 2027-12-01, 2027-12-31
    詳細設計・開発完了            :milestone, m7, 2027-12-31, 0d

    section 工程7: システムテスト
    結合テスト                    :test1, 2028-01-01, 2028-02-15
    システムテスト                :test2, 2028-02-16, 2028-03-31
    システムテスト完了            :milestone, m8, 2028-03-31, 0d

    section 工程8: 受入対応
    研修・受入対応                :accept1, 2028-04-01, 2028-08-31
    受入対応完了                  :milestone, m9, 2028-08-31, 0d

    section 工程9: 移行
    移行準備・データ移行          :mig1, 2027-01-01, 2028-08-31
    移行完了                      :milestone, m10, 2028-08-31, 0d

    section 工程10: 稼働
    本番稼働開始                  :milestone, m11, 2028-09-01, 0d
    本番稼働・安定化              :prod1, 2028-09-01, 2028-12-31
```

## 4. 工程1: 現行調査・分析（準委任）

### 4.1 プロジェクト立ち上げ
- **期間**: 2週間（2026年2月1日～2月14日）
- **担当**: PM1名、PL1名、SE2名、営業1名
- **工数**: 1人月
- **成果物**:
  - プロジェクト計画書

#### 主要作業
- プロジェクト体制構築
- 顧客側担当者との顔合わせ・役割確認

### 4.2 現行システム調査
- **期間**: 6週間（2026年2月15日～3月31日）
- **担当**: SE2名
- **工数**: 3人月
- **成果物**:
  - 現行システム構成図
  - 機能一覧表
  - データベース設計書（現行）
  - 連携システム一覧・連携仕様書

#### 主要作業
##### システム基盤調査（2週間）
- ハードウェア構成調査
- ソフトウェア構成調査（OS、DB、ミドルウェア）
- ネットワーク構成調査
- バックアップ・運用体制調査

##### データベース調査（2週間）
- DB設計書収集・分析
- テーブル構造調査
- データ品質調査（データクレンジング要否判定）

##### 機能調査（1週間）
- 画面一覧・機能一覧作成
- 帳票一覧作成
- バッチ処理一覧作成

##### 連携システム調査（1週間）
- 連携先システム特定
- 連携方式・データ形式調査
- 連携タイミング・頻度調査

### 4.3 業務調査・ヒアリング
- **期間**: 2週間（2026年4月1日～4月15日）
- **担当**: SE2名、営業1名
- **工数**: 1.5人月
- **成果物**:
  - 現行業務フロー図
  - 業務課題一覧
  - ヒアリング議事録

### 4.4 現行調査報告書作成
- **期間**: 2週間（2026年4月16日～4月30日）
- **担当**: SE2名
- **工数**: 0.5人月
- **成果物**:
  - 現行調査報告書

**工程1合計**: 6人月

---

## 5. 工程2: 提案書作成・提案（準委任）

### 5.1 提案書作成
- **期間**: 4週間（2026年5月1日～5月31日）
- **担当**: SE2名
- **工数**: 1人月
- **成果物**:
  - 提案書

**工程2合計**: 1人月

---

## 6. 工程3: 要件定義（準委任）

### 6.1 要件定義計画・体制構築
- **期間**: 4週間（2026年6月1日～6月30日）
- **担当**: SE2名
- **工数**: 1人月

### 6.2 業務要件定義
- **期間**: 10週間（2026年7月1日～9月15日）
- **担当**: SE2名
- **工数**: 5人月

### 6.3 システム要件定義
- **期間**: 6週間（2026年9月16日～10月31日）
- **担当**: SE2名
- **工数**: 3人月

### 6.4 移行要件定義
- **期間**: 4週間（2026年11月1日～11月30日）
- **担当**: SE2名
- **工数**: 1人月

**工程3合計**: 10人月

---

## 7. 工程4: 見積作成・顧客検討

### 7.1 見積作成
- **期間**: 2週間（2026年12月1日～12月14日）
- **担当**: SE2名、営業1名
- **工数**: 0人月
- **成果物**:
  - 見積書一式

### 7.2 顧客検討
- **期間**: 2週間（2026年12月15日～12月31日）
- **担当**: 顧客側
- **工数**: 0人月

**工程4合計**: 0人月

---

## 8. 工程5: 基本設計（請負）

### 8.1 システム構成設計
- **期間**: 8週間（2027年1月1日～2月28日）
- **担当**: SE3名
- **工数**: 6人月

### 8.2 機能設計・画面設計
- **期間**: 8週間（2027年3月1日～4月30日）
- **担当**: SE3名
- **工数**: 6人月

**工程5合計**: 12人月

---

## 9. 工程6: 詳細設計・開発・単体テスト（請負）

### 9.1 詳細設計
- **期間**: 8週間（2027年5月1日～6月30日）
- **担当**: SE3名、PG2名
- **工数**: 12人月

### 9.2 プログラム開発
- **期間**: 20週間（2027年7月1日～11月30日）
- **担当**: SE3名、PG5名
- **工数**: 46人月

### 9.3 単体テスト
- **期間**: 4週間（2027年12月1日～12月31日）
- **担当**: SE2名、PG5名
- **工数**: 6人月

**工程6合計**: 64人月

---

## 10. 工程7: システムテスト（請負）

### 10.1 結合テスト
- **期間**: 6週間（2028年1月1日～2月15日）
- **担当**: SE2名、PG3名
- **工数**: 8人月

### 10.2 システムテスト
- **期間**: 6週間（2028年2月16日～3月31日）
- **担当**: SE2名、PG3名
- **工数**: 12人月

**工程7合計**: 20人月

---

## 11. 工程8: 受入対応（請負）

### 11.1 研修・受入対応
- **期間**: 20週間（2028年4月1日～8月31日）
- **担当**: SE2名、PG4名
- **工数**: 10人月

**工程8合計**: 10人月

---

## 12. 工程9: 移行（請負）

### 12.1 移行準備・データ移行
- **期間**: 80週間（2027年1月1日～2028年8月31日）※基本設計以降と並行実施
- **担当**: SE1名、PG1名
- **工数**: 8人月

**工程9合計**: 8人月

---

## 13. 工程10: 本番稼働
### 13.1 本番稼働・安定化
- **期間**:（2028年09月01日～12月31日）

---

## 14. プロジェクト管理（全期間）

### 14.1 PM・PL管理業務
- **期間**: 全期間（2026年2月1日～2028年9月1日）
- **担当**: PM1名、PL1名
- **工数**: 13人月（全体工数の約10%）

---

## 15. 年度別作業概要

#### 2025年度（2026年2月～2026年3月）
- **主要作業**: 現行調査・分析開始
- **体制**: SE2名、営業1名
- **工数**: 2人月（現行調査2人月）
- **予算**: 1,800,000円
- **成果物**: 現行調査中間報告

#### 2026年度（2026年4月～2027年3月）
- **主要作業**: 現行調査完了～基本設計
- **体制**: SE1-3名、営業1名、PM1名（後半）
- **工数**: 27人月（現行調査4人月 + 提案1人月 + 要件定義10人月 + 基本設計12人月）
- **予算**: 24,300,000円
- **成果物**: 現行調査報告書、提案書、要件定義書、基本設計書

#### 2027年度（2027年4月～2028年3月）
- **主要作業**: 詳細設計・開発～システムテスト完了
- **体制**: PM1名、SE3名、PG5名（段階的増員）
- **工数**: 84人月（詳細・開発・単体64人月 + システムテスト20人月）
- **予算**: 75,600,000円
- **成果物**: 詳細設計書、プログラム一式、テスト結果書

#### 2028年度（2028年4月～2028年9月）
- **主要作業**: 移行・研修・受入対応～本番稼働
- **体制**: PM1名、SE1名、PG1名
- **工数**: 18人月（受入対応10人月 + 移行8人月）
- **予算**: 16,200,000円
- **成果物**: 研修マニュアル、本稼働システム

#### 全期間（プロジェクト管理）
- **主要作業**: プロジェクト全体の管理・統括
- **体制**: PM1名、PL1名
- **工数**: 13人月（全期間を通じた管理業務）
- **予算**: 11,700,000円
- **成果物**: 各種管理資料、進捗報告書、品質管理資料

## 16. 重要な判定ポイント（ゲート）

#### Gate 1: 現行調査完了判定（2026年4月30日）
- **判定基準**:
  - 現行システム構成・機能の把握完了
  - 業務フロー・課題の整理完了
  - 提案作成に必要な情報収集完了
- **判定者**: 顧客側プロジェクト責任者、業務責任者、ベンダー側PM
- **工数**: 6人月（累計）
- **予算**: 5,400,000円（累計）

#### Gate 2: 要件定義完了判定（2026年11月30日）
- **判定基準**:
  - 業務要件・システム要件の合意
  - 移行要件の確定
  - 開発規模・期間の確定
- **判定者**: 顧客側プロジェクト責任者、業務責任者、ベンダー側PM
- **工数**: 17人月（累計：現行調査6 + 提案1 + 要件定義10）
- **予算**: 15,300,000円（累計）

#### Gate 3: 設計完了判定（2027年4月30日）
- **判定基準**:
  - 基本設計書の承認
  - 開発着手可能性の確認
  - IDCFクラウド環境設計の承認
- **判定者**: 顧客側業務責任者、ベンダー側PM・PL・SE
- **工数**: 29人月（累計：Gate2まで17 + 基本設計12）
- **予算**: 26,100,000円（累計）

#### Gate 4: 開発完了判定（2027年12月31日）
- **判定基準**:
  - 全機能の開発完了
  - 単体テスト完了
  - 結合テスト着手可能性確認
- **判定者**: ベンダー側PM・PL・SE
- **工数**: 93人月（累計：Gate3まで29 + 詳細・開発・単体64）
- **予算**: 83,700,000円（累計）

#### Gate 5: 本番稼働判定（2028年8月）
- **判定基準**:
  - 全テスト完了・品質確認
  - データ移行完了・整合性確認
  - IDCFクラウド運用体制準備完了
  - 受入テスト・研修完了
- **判定者**: 顧客側プロジェクト責任者、業務責任者、ベンダー側PM
- **工数**: 144人月（最終：Gate4まで93 + システムテスト20 + 受入テスト10 + 移行8 + 管理13）
- **予算**: 129,600,000円
