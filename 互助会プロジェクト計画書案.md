---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---
# 互助会システム プロジェクト計画書案

## 目次
1. [プロジェクト概要](#1-プロジェクト概要)
   <!-- - 1.1 [背景と目的](#11-背景と目的)
   - 1.2 [プロジェクト範囲](#12-プロジェクト範囲)
   - 1.3 [主要納品物](#13-主要納品物)
   - 1.4 [プロジェクト成功基準](#14-プロジェクト成功基準) -->
2. [プロジェクト体制](#2-プロジェクト体制)
   <!-- - 2.1 [組織構成](#21-組織構成)
   - 2.2 [役割と責任](#22-役割と責任) -->
3. [プロジェクト計画](#3-プロジェクト計画)
   <!-- - 3.1 [全体スケジュール](#31-全体スケジュール)
   - 3.2 [主要マイルストーン](#32-主要マイルストーン) -->
4. [リソース計画](#4-リソース計画)
   <!-- - 4.1 [人員計画](#41-人員計画)
   - 4.2 [予算計画](#42-予算計画) -->
5. [品質管理計画](#5-品質管理計画)
   <!-- - 5.1 [品質基準](#51-品質基準)
   - 5.2 [テスト計画](#52-テスト計画)
   - 5.3 [品質ゲート](#53-品質ゲート)
   - 5.4 [脆弱性診断計画](#54-脆弱性診断計画) -->
6. [リスク管理](#6-リスク管理)
   <!-- - 6.1 [リスク一覧](#61-リスク一覧)
   - 6.2 [リスク対応計画](#62-リスク対応計画) -->
7. [変更管理](#7-変更管理)
   <!-- - 7.1 [変更管理プロセス](#71-変更管理プロセス)
   - 7.2 [変更要求評価基準](#72-変更要求評価基準) -->
8. [コミュニケーション計画](#8-コミュニケーション計画)
   <!-- - 8.1 [会議体制](#81-会議体制)
   - 8.2 [議事録の作成・承認について](#82-議事録の作成承認について)
   - 8.3 [課題管理および対応について](#83-課題管理および対応について) -->
9. [データ移行計画](#9-データ移行計画)
   <!-- - 9.1 [移行対象データ](#91-移行対象データ)
   - 9.2 [移行手順](#92-移行手順) -->
10. [運用保守計画](#10-運用保守計画)
    <!-- - 10.1 [保守体制](#101-保守体制)
    - 10.2 [障害対応フロー](#102-障害対応フロー) -->
11. [教育・研修計画](#11-教育研修計画)
    <!-- - 11.1 [研修スケジュール](#111-研修スケジュール)
    - 11.2 [研修資料作成計画](#112-研修資料作成計画) -->
12. [プロジェクト管理ツール](#12-プロジェクト管理ツール)
    <!-- - 12.1 [使用ツール一覧](#121-使用ツール一覧)
    - 12.2 [ツール活用計画](#122-ツール活用計画) -->
13. [承認](#13-承認)

<!-- pagebreak -->

## 1. プロジェクト概要
**注意事項**: 本計画書の予算は、顧客規模（30万会員）と当社のシステム導入実績に基づいたカスタマイズ機能開発部分の概算です。パッケージ・マスタ設定・研修費用等は含まれていません。この概算予算をもとに、スケジュールと人員計画を立案しております。要件定義後に工数が大きく変動する可能性があるため、正式な予算とスケジュールは要件定義完了後に改めて提出いたします。
### 1.1 背景と目的
- **背景**: 30万会員を抱える現行システムの老朽化と機能拡張の必要性
- **目的**:
  - 互助会パッケージシステムの開発・販売
  - 業務効率化と顧客体験向上
  
- **グループシナジー効果**:
  - **開発・運用コストの最適化**：子会社のパッケージシステムを導入することで、新規開発コストを抑制し、グループ全体のTCO（総所有コスト）を削減
  - **ノウハウの内部蓄積**：システム開発・運用ノウハウをグループ内に蓄積し、将来的な改善や拡張を効率化
  - **迅速な機能拡張**：子会社の既存パッケージをベースとすることで、開発期間を短縮し、市場変化への対応力を強化
  - **保守・運用体制の効率化**：グループ内での一元的な保守・運用体制により、障害対応の迅速化とサービス品質の向上
  - **将来的な事業拡大**：子会社のパッケージ製品の機能強化により、グループ外への販売機会の拡大と新たな収益源の創出

### 1.2 プロジェクト範囲
- **対象システム**: 互助会会員管理システムの再構築
- **対象機能**: 現行システムの機能と改善が必要な機能（要件定義でFix）

### 1.3 主要納品物
- 調査報告書（機能・画面・帳票・外部連携インタフェース一覧、機能改善要件一覧）
- 提案書（カスタマイズ要件一覧(Fit＆Gap)、概算見積書、概算スケジュール）
- 要件定義書一式（画面・帳票要件定義書・外部連携インタフェース要件定義書、データ移行要件定義書、カスタマイズ機能一覧、正式見積書、正式スケジュール）
- 基本設計書一式（画面設計書、帳票設計書、外部連携インタフェース設計書）
- 研修資料一式（システム操作フロー、操作マニュアル）
- 新規パッケージシステム（顧客は本システムの利用権を取得しますが、著作権はベンダーに帰属します。カスタマイズ部分の著作権については個別契約に基づき決定されます。）

<!-- **※著作権に関する注記**：新規パッケージシステムの著作権はベンダーに帰属します。顧客は本システムの利用権を取得しますが、
ソースコードの改変や第三者への提供・販売等の権利は有しません。カスタマイズ部分の著作権については個別契約に基づき決定されます。 -->

<!-- ### 1.4 プロジェクト成功基準
- **システム品質**:
  - テストカバレッジ: 90%以上
  - パフォーマンス要件達成率: 95%
- **プロジェクト管理**:
  - スケジュール遵守率: 90%以上
  - 予算遵守率: 90%以上
- **運用品質**:
  - 障害発生率(安定稼働後): 月3回以下 -->

## 2. プロジェクト体制

### 2.1 組織構成
- **顧客（株式会社くらしの友）**:
  - プロジェクトスポンサー
  - プロジェクト責任者
  - 業務責任者
  - 業務担当者
  - IT責任者
- **ベンダー（株式会社エム・エス・アイ）**:
  - プロジェクトマネージャー(PM)
  - プロジェクトリーダー(PL)
  - システムエンジニア(SE)
  - プログラマー（PG）
  - 営業担当者

### 2.2 役割と責任
| 役割 | 顧客側<br>ベンダー側 | 責任範囲 |
|------|------------------|----------|
| プロジェクトスポンサー | 顧客 | 予算承認・重要意思決定・プロジェクト推進支援 |
| プロジェクト責任者 | 顧客 | プロジェクト統括・進捗確認・課題解決判断 | 
| 業務責任者 | 顧客 | 業務要件の決定・業務担当者の調整・受入テスト承認 | 
| 業務担当者 | 顧客 | 業務要件の提供・現行業務説明・受入テスト実施 | 
| IT責任者 | 顧客 | 導入全般の支援・社内IT部門との調整 | 
| プロジェクトマネージャー | ベンダー | 予算管理・進捗管理・リスク管理・顧客折衝 | 
| プロジェクトリーダー | ベンダー | スコープ管理・品質管理・開発チーム管理 |
| システムエンジニア | ベンダー | 要件ヒアリング・提案・基本設計・システムテスト・課題管理| 
| プログラマー | ベンダー | 詳細設計・開発・単体・結合テスト・不具合修正 |
| 営業担当者 | ベンダー | 契約管理・提案・予算調整・顧客折衝| 

## 3. プロジェクト計画

### 3.1 全体スケジュール
```mermaid
gantt
    title 互助会システム再構築 全体スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section 1: 現行調査
    現行調査 :survey, 2026-02-01, 2026-04-30

    section 2: 提案
    提案 : prop, 2026-05-01, 2026-05-31

    section 3: 要件定義
    要件定義 : req, 2026-06-01, 2026-11-30

    section 4: 見積作成・顧客検討
    見積作成 : estimate, 2026-12-01, 2026-12-14
    顧客検討 : review, 2026-12-15, 2026-12-31

    section 5: 設計・開発・テスト
    設計・開発・テスト : design, 2027-01-01, 2028-03-31

    section 6: データ移行
    データ移行 : migrate, 2027-01-01, 2028-08-31

    section 7: 研修・受入・並行
    研修・受入・並行 : test, 2028-04-01, 2028-08-31

    section 8: 稼働
    稼働 : prod, 2028-09-01, 2028-12-31
```

### 3.2 主要マイルストーン
| マイルストーン | 開始予定日 | 終了予定日 | 期間 | 契約形態 | 成果物 |
|---------------|------------|------------|------|----------|--------|
| 現行調査 | 2026-02-01 | 2026-04-30 | 3ヶ月 | 準委任 | 調査報告書 |
| 提案 | 2026-05-01 | 2026-05-31 | 1ヶ月 | 準委任 | 提案書 |
| 要件定義 | 2026-06-01 | 2026-11-30 | 6ヶ月 | 準委任 | 要件定義書一式 |
| 見積作成 | 2026-12-01 | 2026-12-14 | 2週間 | - | 見積書 |
| 顧客検討 | 2026-12-15 | 2026-12-31 | 2週間 | - | - |
| 基本設計 | 2027-01-01 | 2027-04-30 | 4ヶ月 | 請負 | 基本設計書一式 |
| 詳細・開発・単体 | 2027-05-01 | 2027-12-31 | 8ヶ月 | 請負 | プログラム一式 |
| システムテスト | 2028-01-01 | 2028-03-31 | 3ヶ月 | 請負 | テスト結果一式|
| 研修・受入・並行 | 2028-04-01 | 2028-08-31 | 5ヶ月 | 請負 | 研修資料一式 |
| データ移行 | 2027-01-01 | 2028-08-31 | 20ヶ月 | 請負 | コンバートデータ |
| 本番稼働開始 | 2028-09-01 | - | - | - | - |

## 4. リソース計画

### 4.1 人員計画
- **総工数**: 144人月
- **ピーク時体制**: SE3名 + PG5名 

### 4.2 予算計画

#### 4.2.1 イニシャル予算（初期開発費用）
- **イニシャル総予算**: 129,600,000円（144人月 × 人月単価900,000円で計算）
- **内訳**:
  - 現行調査: 5,400,000円　(6人月)
  - 提案: 900,000円　(1人月)
  - 要件定義: 9,000,000円　(10人月)
  - 基本設計: 10,800,000円　(12人月)
  - 詳細・開発・単体: 57,600,000円　(64人月)
  - システムテスト: 18,000,000円　(20人月)
  - 研修・受入対応: 9,000,000円　(10人月)
  - データ移行: 7,200,000円　(8人月)
  - 管理: 11,700,000円　(13人月、全体工数の10%程度)

#### 4.2.2 ランニング予算（運用保守費用）
- **年間ランニング費用**: 13,275,000円
- **5年間総額**: 66,375,000円

**ランニング費用内訳（年額）**:
- システム保守費用: 11,475,000円
  - 基準額: 76,500,000円（基本設計 + 詳細・開発・単体 + データ移行の合計：85人月 × 人月単価900,000円で計算）
  - 保守率: 15%（76,500,000円 × 15% = 11,475,000円）
- サーバ使用料: 1,800,000円（月額150,000円 × 12ヶ月）

<!-- pagebreak -->

**5年間ランニング予算**:
| 年度 | システム保守費用 | サーバ使用料 | 年間合計 |
|------|------------------|--------------|----------|
| 1年目 | 11,475,000円 | 1,800,000円 | 13,275,000円 |
| 2年目 | 11,475,000円 | 1,800,000円 | 13,275,000円 |
| 3年目 | 11,475,000円 | 1,800,000円 | 13,275,000円 |
| 4年目 | 11,475,000円 | 1,800,000円 | 13,275,000円 |
| 5年目 | 11,475,000円 | 1,800,000円 | 13,275,000円 |
| **合計** | **57,375,000円** | **9,000,000円** | **66,375,000円** |

#### 4.2.3 総予算（イニシャル + ランニング5年分）
- **総予算**: 195,975,000円
  - イニシャル予算: 129,600,000円
  - ランニング予算（5年分）: 66,375,000円

### 4.3 工程別リソース計画

| 工程 | 期間 | 担当者数 | 総人月 |
|---------|------|----------|--------|
| 1. 現行調査 | 3ヶ月 | SE2名 | 6人月 |
| 2. 提案書作成 | 2ヶ月 | SE2名 | 1人月 |
| 3. 要件定義 | 5ヶ月 | SE2名 | 10人月 |
| 4. 基本設計 | 4ヶ月 | SE3名 | 12人月 |
| 5. 詳細設計・開発・単体テスト | 8ヶ月 | SE3名+PG5名 | 64人月 |
| 6. システムテスト | 4ヶ月 | SE2名+PG3名 | 20人月 |
| 7. 研修・受入・並行稼働対応 | 5ヶ月 | SE2名+PG2名 | 10人月 |
| 8. データ移行 | 20ヶ月 | SE1名+PG1名 | 8人月 |
| 9. プロジェクト管理 | 全期間 | PM1名+PL1名 | 13 人月 |
| **合計** | 約31ヶ月 | - | **144人月** |
## 5. 品質管理計画

### 5.1 品質基準

#### 5.1.1 可用性基準
- **SLA**: 99.9%（月間ダウンタイム43分以内）

#### 5.1.2 性能基準
- **レスポンスタイム**:
  - 通常処理: 3秒以内（90%ile）
  - 検索処理: 3秒以内（90%ile）
  - 帳票出力: 10秒以内（90%ile）

#### 5.1.3 セキュリティ基準
- **ネットワークセキュリティ**:
  - IDCFクラウドのファイアウォール機能
  - WAF（Web Application Firewall）検討
  - VPN接続による専用アクセス検討
  - クライアント認証機能
  - IP制限機能
- **データセキュリティ**:
  - SSL/TLS暗号化通信（TLS1.2以上）
  - パスワード暗号化
- **アクセス制御**:
  - ロールベースアクセス制御（RBAC）
  - アクセスログの記録・監査

#### 5.1.4 運用基準
- **監視体制**: 24時間365日監視
- **バックアップ**:
  - 日次フルバックアップ
  - 遠隔地への複製保存
- **災害対策**:
  - RTO（目標復旧時間）: 2時間以内
  - RPO（目標復旧ポイント）: 24時間以内

### 5.2 テスト計画

#### 5.2.1 サーバ環境
- **開発環境**:
  - サーバ: 1台（アプリケーション、データベース）
- **テスト環境**:
  - サーバ: 1台（アプリケーション、データベース）
- **本番環境**:
  - サーバ: 1台（アプリケーション、データベース）

#### 5.2.2 テスト種類と実施計画
| テスト種類 | 実施時期 | 実施者 | 成果物 |
|------------|----------|--------|--------|
| 単体テスト | 開発工程 | 開発者 | 単体テスト結果 |
| 結合テスト | 開発工程 | 開発者 | 結合テスト結果 |
| システムテスト | システムテスト工程 | テストチーム | システムテスト結果 |
| 性能テスト | システムテスト工程 | テストチーム | 性能テスト結果 |
| セキュリティテスト | システムテスト工程 | テストチーム | セキュリティテスト結果 |
| 受入テスト | 受入テスト工程 | 顧客 | 受入テスト結果 |

#### 5.2.3 テストデータ
- **テストデータ作成計画**:
  - 現行システムからのデータ抽出
  - テストデータのバリエーション作成

### 5.3 品質ゲート
| ゲート | 時期 | チェック項目 | 責任者 |
|--------|------|--------------|--------|
| 要件定義完了 | 2026-11 | 業務システム要件の合意 | 顧客側：業務責任者<br>ベンダー側：PM |
| 基本設計完了 | 2027-04 | 基本設計書の承認 | 顧客側：業務責任者<br>ベンダー側：PM、PL |
| システムテスト完了 | 2028-03 | テスト結果の承認 | ベンダー側：PM、PL |
| 受入完了 | 2028-08 | 受入テストの承認 | 顧客側：プロジェクト責任者|

### 5.4 脆弱性診断計画
- システムリリース前に第三者機関または専門ツールによる脆弱性診断を実施する。
- 診断対象はWebアプリケーションを対象とし、OWASP Top10の主要な脆弱性をカバーする。
- 指摘事項はリリース前に是正対応を行い、対応結果を記録・報告する。

## 6. リスク管理

### 6.1 リスク一覧
| リスク | 発生確率 | 影響度 | 対策 | 
|--------|----------|--------|------|
| 要件変更の頻発 | 高 | 高 | 変更管理プロセスの徹底 | 
| スケジュール遅延 | 中 | 高 | 進捗管理の強化 | 
| データ移行の失敗 | 中 | 高 | 複数回移行テストの実施及び検証 | 
| ユーザー受入れ問題 | 高 | 中 | 十分な教育・研修期間の確保 |

### 6.2 リスク対応計画
- **リスク監視体制**:リスクの発生頻度、影響度を定期的にレビューし、適切な対応を実施する。

## 7. 変更管理
### 7.1 変更管理プロセス
要件定義終了後の工程において変更が発生した場合は、以下の方針で対応する：
- 微調整レベルの変更: 現フェーズ内で対応し、スケジュールや予算への影響を最小限に抑える
- 大きな変更: 重要度に応じてスケジュール調整または別フェーズ対応を検討する。
追加の工数・期間・予算を算定した上で顧客と協議する。

変更管理の手順：
1. 変更要求の提出
2. 変更規模の判定（微調整 or 大きな変更）
3. 影響度分析（工数・期間・予算への影響）
4. 承認プロセス
5. 実装と検証

### 7.2 変更要求評価基準

- **重要度**:
  - 必須（システム稼働に必須）
  - 重要（業務に重大な影響）
  - 普通（業務に影響あり）
  - 低（業務に影響なし）

## 8. コミュニケーション計画

### 8.1 会議体制
| 会議種別 | 頻度 | 参加者 | 議題 |実施期間 |
|----------|------|--------|------|------|
| 現行調査会議 | 週2回 | 顧客、SE | 現行調査、提案 |現行調査期間 |
| 要件定義会議 | 週1回 | 顧客、SE（PM、PL）| 要件ヒアリング、提案、課題共有 |要件定義期間 |
| 課題会議 | 隔週 | 顧客、SE（PM、PL） | 課題確認、進捗報告 |基本設計から安定稼働まで |
| 週次進捗会議 | 週1回 | 開発チーム | 週間進捗報告 |プロジェクト期間中 |
| 仕様検討会議 | 随時 | 開発チーム | 仕様・課題の検討 |プロジェクト期間中 |

### 8.2 議事録の作成・承認について
要件定義等の議事録は、ベンダーが作成し、会議終了後速やかに顧客に提出するものとします。
提出された議事録は、受領日から1週間以内に承認または修正依頼を行うものとし、1週間を経過しても承認または修正依頼がない場合は、内容に異議がないものとして自動的に承認されたものとみなします。

### 8.3 課題管理および対応について
プロジェクト期間中に発生した課題については、Googleスプレッドシート等の管理表で一元管理します。
各課題には対応期限を設定し、関係者は期間内に回答・対応する協力義務を負うものとします。

## 9. データ移行計画

### 9.1 移行対象データ
- 互助会会員システムの会員データ一式（要件定義でFix）

### 9.2 データ移行手順
1. 移行データの洗い出し
2. データクレンジング
3. 移行プログラム開発
4. 移行テスト（検証環境）
   - 移行期間中、定期的にに検証環境へのコンバートを実施
   - 各回のコンバート後にデータ整合性検証を実施
   - 検証結果に基づき移行プログラムを継続的に改善
5. 移行リハーサル（本番環境想定での総合検証）
6. 受入時のデータ移行
   - データ件数確認
   - データ項目の整合性確認
   - 業務担当者による業務テスト
7. 本番移行
8. 移行後検証
   - データ件数確認
   - データ項目の整合性確認
   - 業務担当者による目視確認

## 10. 運用保守計画

### 10.1 保守体制
- **通常保守**:
  - 窓口対応時間: 平日（月～金曜日）9:00～17:30
  - 担当: 保守対応チーム
  - 対応: 通常の問い合わせ、障害対応

- **時間外対応（一時対応）**:
  - 平日（月～金曜日）17:30～20:00
  - 土日祝日 9:00～17:30
  - 担当: カスタマサポートチーム

- **E-mailによる受付**:
  - 24時間365日 ※土日祝日、年末年始の対応は翌営業日以降

- **緊急保守**:
  - 担当: 緊急対応チーム
  - 対応: システム停止等の重大な障害

### 10.2 障害対応フロー
1. 障害の報告、検知
2. 保守対応
3. 障害の解決
4. 再発防止策の実施

## 11. 教育・研修計画

### 11.1 研修スケジュール
| 研修種別 | 時期 | 対象者 | 内容 |
|----------|------|--------|------|
| 操作研修 | 2028-04 | 業務担当者 | 実務での利用方法 |
| 管理者研修 | 2028-04 | 業務責任者 | 管理機能 |

### 11.2 研修資料作成計画
- システム操作フロー
- 操作マニュアル

## 12. プロジェクト管理ツール

### 12.1 使用ツール一覧
| 用途 | ツール名 | 用途詳細 |
|------|----------|----------|
| プロジェクト管理 | Googleスプレッドシート | 課題管理、予算管理、進捗管理 |
| タスク管理 | Redmine | タスク管理、チケット管理 |
| ソース・ドキュメント管理 | SVN | バージョン管理 |
| コミュニケーション | Zoom | チーム内コミュニケーション |
| 会議 | Zoom 、Google Meet | オンライン会議 |
| ファイル共有 | BOX | 顧客との資料・データの共有 |

### 12.2 ツール活用計画

- **プロジェクト管理**: Googleスプレッドシート
  - 課題管理
  - 進捗管理
  - 予算管理
  - リソース計画
  - リリース管理

- **Redmine**:
  - タスク(WBS)の作成とアサイン
  - チケットの管理
  - 課題のトラッキング
  - ナレッジベースの構築

- **SVN**:
  - プロジェクト文書の管理
  - 会議議事録の管理
  - ソースコードのバージョン管理

- **Zoom**:
  - 日常的なコミュニケーション
  - チーム会議

- **BOX**:
  - 顧客との資料・データの共有

## 13. 承認

| 役　職                 | 氏　　名 | 署　　名 | 承認日 |
|----------------------|------|------|--------|
| プロジェクトスポンサー |      |      |        |
| プロジェクト責任者     |      |      |        |
| 業務責任者             |      |      |        |
| IT責任者               |      |      |        |
| プロジェクトマネージャー |      |      |        |
| プロジェクトリーダー   |      |      |        |